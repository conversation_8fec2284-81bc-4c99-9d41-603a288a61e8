<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Define the filter structure for organizing files in Solution Explorer -->
  <!-- Module-first organization to match folder structure -->
  <!-- Main module filters -->
  <ItemGroup>
    <Filter Include="World">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="Authentication">
      <UniqueIdentifier>{B2C3D4E5-F6A7-8901-BCDE-F23456789012}</UniqueIdentifier>
    </Filter>
    <Filter Include="Combat">
      <UniqueIdentifier>{C3D4E5F6-A7B8-9012-CDEF-345678901234}</UniqueIdentifier>
    </Filter>
    <Filter Include="Database">
      <UniqueIdentifier>{D4E5F6A7-B8C9-0123-DEF4-456789012345}</UniqueIdentifier>
    </Filter>
    <Filter Include="Economy">
      <UniqueIdentifier>{E5F6A7B8-C9D0-1234-EF56-567890123456}</UniqueIdentifier>
    </Filter>
    <Filter Include="Items">
      <UniqueIdentifier>{F6A7B8C9-D0E1-2345-F678-678901234567}</UniqueIdentifier>
    </Filter>
    <Filter Include="Network">
      <UniqueIdentifier>{A7B8C9D0-E1F2-3456-789A-789012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Player">
      <UniqueIdentifier>{B8C9D0E1-F2A3-4567-89AB-890123456789}</UniqueIdentifier>
    </Filter>
    <Filter Include="Security">
      <UniqueIdentifier>{C9D0E1F2-A3B4-5678-9ABC-901234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="System">
      <UniqueIdentifier>{D0E1F2A3-B4C5-6789-ABCD-012345678901}</UniqueIdentifier>
    </Filter>
    <Filter Include="Build Files">
      <UniqueIdentifier>{E12C6939-2E9C-4954-A4F9-85D5A5D5A5D5}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <!-- Subfilters for each module -->
  <ItemGroup>
    <!-- World Module Subfilters -->
    <Filter Include="World\Headers">
      <UniqueIdentifier>{E1F2A3B4-C5D6-789A-BCDE-123456789012}</UniqueIdentifier>
    </Filter>
    <Filter Include="World\Source">
      <UniqueIdentifier>{F1F2A3B4-C5D6-789A-BCDE-123456789012}</UniqueIdentifier>
    </Filter>
    <Filter Include="World\Documents">
      <UniqueIdentifier>{CBDCEDF1-F123-2345-6789-123456789012}</UniqueIdentifier>
    </Filter>
    <!-- Authentication Module Subfilters -->
    <Filter Include="Authentication\Headers">
      <UniqueIdentifier>{F2A3B4C5-D6E7-89AB-CDEF-234567890123}</UniqueIdentifier>
    </Filter>
    <Filter Include="Authentication\Source">
      <UniqueIdentifier>{G2A3B4C5-D6E7-89AB-CDEF-234567890123}</UniqueIdentifier>
    </Filter>
    <Filter Include="Authentication\Documents">
      <UniqueIdentifier>{DCEDF123-1234-3456-789A-234567890123}</UniqueIdentifier>
    </Filter>
    <!-- Combat Module Subfilters -->
    <Filter Include="Combat\Headers">
      <UniqueIdentifier>{A3B4C5D6-E7F8-9ABC-DEF1-345678901234}</UniqueIdentifier>
    </Filter>
    <Filter Include="Combat\Source">
      <UniqueIdentifier>{H3B4C5D6-E7F8-9ABC-DEF1-345678901234}</UniqueIdentifier>
    </Filter>
    <Filter Include="Combat\Documents">
      <UniqueIdentifier>{EDF12341-2345-4567-89AB-345678901234}</UniqueIdentifier>
    </Filter>
    <!-- Database Module Subfilters -->
    <Filter Include="Database\Headers">
      <UniqueIdentifier>{B4C5D6E7-F8A9-ABCD-EF12-456789012345}</UniqueIdentifier>
    </Filter>
    <Filter Include="Database\Source">
      <UniqueIdentifier>{I4C5D6E7-F8A9-ABCD-EF12-456789012345}</UniqueIdentifier>
    </Filter>
    <Filter Include="Database\Documents">
      <UniqueIdentifier>{F1234123-3456-5678-9ABC-456789012345}</UniqueIdentifier>
    </Filter>
    <!-- Economy Module Subfilters -->
    <Filter Include="Economy\Headers">
      <UniqueIdentifier>{C5D6E7F8-A9BA-BCDE-F123-567890123456}</UniqueIdentifier>
    </Filter>
    <Filter Include="Economy\Source">
      <UniqueIdentifier>{J5D6E7F8-A9BA-BCDE-F123-567890123456}</UniqueIdentifier>
    </Filter>
    <Filter Include="Economy\Documents">
      <UniqueIdentifier>{12341234-4567-6789-ABCD-567890123456}</UniqueIdentifier>
    </Filter>
    <!-- Items Module Subfilters -->
    <Filter Include="Items\Headers">
      <UniqueIdentifier>{D6E7F8A9-BACB-CDEF-1234-678901234567}</UniqueIdentifier>
    </Filter>
    <Filter Include="Items\Source">
      <UniqueIdentifier>{K6E7F8A9-BACB-CDEF-1234-678901234567}</UniqueIdentifier>
    </Filter>
    <Filter Include="Items\Documents">
      <UniqueIdentifier>{23412345-5678-789A-BCDE-678901234567}</UniqueIdentifier>
    </Filter>
    <!-- Network Module Subfilters -->
    <Filter Include="Network\Headers">
      <UniqueIdentifier>{E7F8A9BA-CBDC-DEF1-2345-789012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Network\Source">
      <UniqueIdentifier>{L7F8A9BA-CBDC-DEF1-2345-789012345678}</UniqueIdentifier>
    </Filter>
    <Filter Include="Network\Documents">
      <UniqueIdentifier>{34123456-6789-89AB-CDEF-789012345678}</UniqueIdentifier>
    </Filter>
    <!-- Player Module Subfilters -->
    <Filter Include="Player\Headers">
      <UniqueIdentifier>{F8A9BACB-DCED-EF12-3456-890123456789}</UniqueIdentifier>
    </Filter>
    <Filter Include="Player\Source">
      <UniqueIdentifier>{M8A9BACB-DCED-EF12-3456-890123456789}</UniqueIdentifier>
    </Filter>
    <Filter Include="Player\Documents">
      <UniqueIdentifier>{41234567-789A-9ABC-DEF1-890123456789}</UniqueIdentifier>
    </Filter>
    <!-- Security Module Subfilters -->
    <Filter Include="Security\Headers">
      <UniqueIdentifier>{A9BACBDC-EDFE-F123-4567-901234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="Security\Source">
      <UniqueIdentifier>{N9BACBDC-EDFE-F123-4567-901234567890}</UniqueIdentifier>
    </Filter>
    <Filter Include="Security\Documents">
      <UniqueIdentifier>{12345678-89AB-ABCD-EF12-901234567890}</UniqueIdentifier>
    </Filter>
    <!-- System Module Subfilters -->
    <Filter Include="System\Headers">
      <UniqueIdentifier>{BACBDCED-FEF1-1234-5678-012345678901}</UniqueIdentifier>
    </Filter>
    <Filter Include="System\Source">
      <UniqueIdentifier>{OACBDCED-FEF1-1234-5678-012345678901}</UniqueIdentifier>
    </Filter>
    <Filter Include="System\Documents">
      <UniqueIdentifier>{23456789-9ABC-BCDE-F123-012345678901}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <!-- World Module Files -->
  <ItemGroup>
    <!-- World Module Headers -->
    <ClInclude Include="world\Headers\MonsterEventRespawn.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\WorldAvatarEntry.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\WorldAvatarExit.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\WorldServiceInform.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\EnterWorldResult.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\AlterWorldService.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\OpenWorldSuccessResult.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\OpenWorldFailureResult.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\EnterWorldRequest.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\ExitWorldRequest.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CreateCMonster.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMonsterAI.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMapData.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMapOperation.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMapDisplay.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMapExtend.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMapTab.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\BossScheduleMap.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CCircleZone.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMonster.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMonsterAggroMgr.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMonsterHierarchy.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\MonsterStateData.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\MonsterSFContDamageTolerance.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <ClInclude Include="world\Headers\CMonsterEventSet.h">
      <Filter>World\Headers</Filter>
    </ClInclude>
    <!-- World Module Sources -->
    <ClCompile Include="world\Source\MonsterEventRespawn.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\WorldAvatarEntry.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\WorldAvatarExit.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\WorldServiceInform.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\EnterWorldResult.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\AlterWorldService.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\OpenWorldSuccessResult.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\OpenWorldFailureResult.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\EnterWorldRequest.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\ExitWorldRequest.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CreateCMonster.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMonsterAI.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMapData.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMapOperation.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMapDisplay.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMapExtend.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMapTab.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\BossScheduleMap.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CCircleZone.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMonster.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMonsterAggroMgr.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMonsterHierarchy.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\MonsterStateData.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\MonsterSFContDamageTolerance.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <ClCompile Include="world\Source\CMonsterEventSet.cpp">
      <Filter>World\Source</Filter>
    </ClCompile>
    <!-- World Module Documentation -->
    <None Include="world\Documents\README.md">
      <Filter>World\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Authentication Module Files -->
  <ItemGroup>
    <ClInclude Include="authentication\Headers\**\*.h">
      <Filter>Authentication\Headers</Filter>
    </ClInclude>
    <ClInclude Include="authentication\Headers\**\*.hpp">
      <Filter>Authentication\Headers</Filter>
    </ClInclude>
    <ClCompile Include="authentication\Source\**\*.cpp">
      <Filter>Authentication\Source</Filter>
    </ClCompile>
    <ClCompile Include="authentication\Source\**\*.c">
      <Filter>Authentication\Source</Filter>
    </ClCompile>
    <None Include="authentication\Documents\**\*.md">
      <Filter>Authentication\Documents</Filter>
    </None>
    <None Include="authentication\Documents\**\*.txt">
      <Filter>Authentication\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Combat Module Files -->
  <ItemGroup>
    <ClInclude Include="combat\Headers\**\*.h">
      <Filter>Combat\Headers</Filter>
    </ClInclude>
    <ClInclude Include="combat\Headers\**\*.hpp">
      <Filter>Combat\Headers</Filter>
    </ClInclude>
    <ClCompile Include="combat\Source\**\*.cpp">
      <Filter>Combat\Source</Filter>
    </ClCompile>
    <ClCompile Include="combat\Source\**\*.c">
      <Filter>Combat\Source</Filter>
    </ClCompile>
    <None Include="combat\Documents\**\*.md">
      <Filter>Combat\Documents</Filter>
    </None>
    <None Include="combat\Documents\**\*.txt">
      <Filter>Combat\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Database Module Files -->
  <ItemGroup>
    <ClInclude Include="database\Headers\**\*.h">
      <Filter>Database\Headers</Filter>
    </ClInclude>
    <ClInclude Include="database\Headers\**\*.hpp">
      <Filter>Database\Headers</Filter>
    </ClInclude>
    <ClCompile Include="database\Source\**\*.cpp">
      <Filter>Database\Source</Filter>
    </ClCompile>
    <ClCompile Include="database\Source\**\*.c">
      <Filter>Database\Source</Filter>
    </ClCompile>
    <None Include="database\Documents\**\*.md">
      <Filter>Database\Documents</Filter>
    </None>
    <None Include="database\Documents\**\*.txt">
      <Filter>Database\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Economy Module Files -->
  <ItemGroup>
    <ClInclude Include="economy\Headers\**\*.h">
      <Filter>Economy\Headers</Filter>
    </ClInclude>
    <ClInclude Include="economy\Headers\**\*.hpp">
      <Filter>Economy\Headers</Filter>
    </ClInclude>
    <ClCompile Include="economy\Source\**\*.cpp">
      <Filter>Economy\Source</Filter>
    </ClCompile>
    <ClCompile Include="economy\Source\**\*.c">
      <Filter>Economy\Source</Filter>
    </ClCompile>
    <None Include="economy\Documents\**\*.md">
      <Filter>Economy\Documents</Filter>
    </None>
    <None Include="economy\Documents\**\*.txt">
      <Filter>Economy\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Items Module Files -->
  <ItemGroup>
    <ClInclude Include="items\Headers\**\*.h">
      <Filter>Items\Headers</Filter>
    </ClInclude>
    <ClInclude Include="items\Headers\**\*.hpp">
      <Filter>Items\Headers</Filter>
    </ClInclude>
    <ClCompile Include="items\Source\**\*.cpp">
      <Filter>Items\Source</Filter>
    </ClCompile>
    <ClCompile Include="items\Source\**\*.c">
      <Filter>Items\Source</Filter>
    </ClCompile>
    <None Include="items\Documents\**\*.md">
      <Filter>Items\Documents</Filter>
    </None>
    <None Include="items\Documents\**\*.txt">
      <Filter>Items\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Network Module Files -->
  <ItemGroup>
    <ClInclude Include="network\Headers\**\*.h">
      <Filter>Network\Headers</Filter>
    </ClInclude>
    <ClInclude Include="network\Headers\**\*.hpp">
      <Filter>Network\Headers</Filter>
    </ClInclude>
    <ClCompile Include="network\Source\**\*.cpp">
      <Filter>Network\Source</Filter>
    </ClCompile>
    <ClCompile Include="network\Source\**\*.c">
      <Filter>Network\Source</Filter>
    </ClCompile>
    <None Include="network\Documents\**\*.md">
      <Filter>Network\Documents</Filter>
    </None>
    <None Include="network\Documents\**\*.txt">
      <Filter>Network\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Player Module Files -->
  <ItemGroup>
    <ClInclude Include="player\Headers\**\*.h">
      <Filter>Player\Headers</Filter>
    </ClInclude>
    <ClInclude Include="player\Headers\**\*.hpp">
      <Filter>Player\Headers</Filter>
    </ClInclude>
    <ClCompile Include="player\Source\**\*.cpp">
      <Filter>Player\Source</Filter>
    </ClCompile>
    <ClCompile Include="player\Source\**\*.c">
      <Filter>Player\Source</Filter>
    </ClCompile>
    <None Include="player\Documents\**\*.md">
      <Filter>Player\Documents</Filter>
    </None>
    <None Include="player\Documents\**\*.txt">
      <Filter>Player\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Security Module Files -->
  <ItemGroup>
    <ClInclude Include="security\Headers\**\*.h">
      <Filter>Security\Headers</Filter>
    </ClInclude>
    <ClInclude Include="security\Headers\**\*.hpp">
      <Filter>Security\Headers</Filter>
    </ClInclude>
    <ClCompile Include="security\Source\**\*.cpp">
      <Filter>Security\Source</Filter>
    </ClCompile>
    <ClCompile Include="security\Source\**\*.c">
      <Filter>Security\Source</Filter>
    </ClCompile>
    <None Include="security\Documents\**\*.md">
      <Filter>Security\Documents</Filter>
    </None>
    <None Include="security\Documents\**\*.txt">
      <Filter>Security\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- System Module Files -->
  <ItemGroup>
    <ClInclude Include="system\Headers\**\*.h">
      <Filter>System\Headers</Filter>
    </ClInclude>
    <ClInclude Include="system\Headers\**\*.hpp">
      <Filter>System\Headers</Filter>
    </ClInclude>
    <ClCompile Include="system\Source\**\*.cpp">
      <Filter>System\Source</Filter>
    </ClCompile>
    <ClCompile Include="system\Source\**\*.c">
      <Filter>System\Source</Filter>
    </ClCompile>
    <None Include="system\Documents\**\*.md">
      <Filter>System\Documents</Filter>
    </None>
    <None Include="system\Documents\**\*.txt">
      <Filter>System\Documents</Filter>
    </None>
  </ItemGroup>
  <!-- Build and Documentation Files -->
  <ItemGroup>
    <None Include="CMakeLists.txt">
      <Filter>Build Files</Filter>
    </None>
  </ItemGroup>
</Project>