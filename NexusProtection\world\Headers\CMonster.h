#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>

// Forward declarations
class <PERSON>haracter;
class CGameObject;
class CLootingMgr;
class CMonsterAggroMgr;
class CMonsterHierarchy;
class MonsterSFContDamageToleracne;
class EmotionPresentation<PERSON>hecker;
class MonsterStateData;
class CMonsterSkillPool;
class CMonsterAI;
class CLuaSignalReActor;
class _effect_parameter;
struct _monster_fld;
struct _event_respawn;
struct _event_set;
struct _mon_active;
struct _dummy_position;

// Virtual function table structure
struct CGameObjectVtbl {
    void* functions[64]; // Placeholder for virtual function pointers
};

/**
 * @class CMonster
 * @brief Monster entity class for game world
 * 
 * This class represents a monster entity in the game world, handling
 * AI, combat, movement, state management, and lifecycle. It inherits from
 * CCharacter and provides extensive functionality for monster behavior.
 * 
 * Key Features:
 * - Monster AI and state machine
 * - Combat and aggro management
 * - Movement and rotation control
 * - Emotion and state tracking
 * - Lifecycle and respawn management
 * - Hierarchy for parent/child relationships
 * 
 * @note Refactored from decompiled CMonster structure
 */
class CMonster : public CCharacter {
public:
    // Constants
    static constexpr int MAX_EMOTION_STATES = 8;
    static constexpr int EMOTION_STATE_MASK = 0x7;
    static constexpr int EMOTION_STATE_SHIFT = 2;
    static constexpr int EMOTION_STATE_BITS = 0xFFFFFFE3;
    static constexpr uint32_t INVALID_DESTROY_TIME = 0xFFFFFFFF;

    /**
     * @brief Default constructor
     * Initializes the monster with default values
     */
    CMonster();

    /**
     * @brief Virtual destructor
     * Cleans up all allocated resources
     */
    virtual ~CMonster();

    /**
     * @brief Main update loop for monster logic
     * Processes AI, movement, combat, and state changes
     */
    void Loop();

    /**
     * @brief Checks if the monster can move
     * @return true if the monster has a non-zero movement speed
     */
    bool IsMovable() const;

    /**
     * @brief Gets the current movement speed
     * @return Current movement speed based on state
     */
    float GetMoveSpeed() const;

    /**
     * @brief Gets the current movement type
     * @return Current movement type (0 = normal, 1 = combat)
     */
    uint8_t GetMoveType() const;

    /**
     * @brief Sets the movement type
     * @param moveType Movement type to set
     */
    void SetMoveType(uint8_t moveType);

    /**
     * @brief Gets the current HP
     * @return Current health points
     */
    virtual int GetHP() const;

    /**
     * @brief Gets the maximum HP
     * @return Maximum health points
     */
    virtual int GetMaxHP() const;

    /**
     * @brief Sets the HP value
     * @param hp New HP value
     * @param notify Whether to notify about the change
     * @return true if HP was set successfully
     */
    bool SetHP(int hp, bool notify = true);

    /**
     * @brief Gets the base HP recovery amount from effect parameters
     * @return Base HP recovery amount per tick
     */
    int GetBaseHPRecovery() const;

    /**
     * @brief Gets the current emotion state
     * @return Current emotion state (0-7)
     */
    uint8_t GetEmotionState() const;

    /**
     * @brief Sets the emotion state
     * @param emotionState New emotion state (0-7)
     */
    void SetEmotionState(uint8_t emotionState);

    /**
     * @brief Gets the combat state
     * @return Current combat state
     */
    uint8_t GetCombatState() const;

    /**
     * @brief Sets the combat state
     * @param combatState New combat state
     */
    void SetCombatState(uint8_t combatState);

    /**
     * @brief Creates AI for the monster
     * @param aiType Type of AI to create
     * @return true if AI was created successfully
     */
    bool CreateAI(int aiType);

    /**
     * @brief Checks and processes monster rotation
     */
    void CheckMonsterRotate();

    /**
     * @brief Checks and processes auto HP recovery
     */
    void CheckAutoRecoverHP();

    /**
     * @brief Performs automatic HP recovery with optional bonus
     * @param bonusRecovery Additional recovery amount (default: 0.0f)
     *
     * This method handles the automatic HP recovery for monsters, including:
     * - Base recovery from effect parameters
     * - Optional bonus recovery amount
     * - Minimum HP threshold checks (10% of max HP)
     * - HP bounds validation
     */
    void AutoRecover(float bonusRecovery = 0.0f);

    /**
     * @brief Checks and processes emotion presentation
     */
    void CheckEmotionPresentation();

    /**
     * @brief Checks if the monster should be destroyed after delay
     * @return true if the monster should be destroyed
     */
    bool CheckDelayDestroy();

    /**
     * @brief Checks if respawn process should be initiated
     * @return true if respawn process should start
     */
    bool CheckRespawnProcess();

    /**
     * @brief Checks monster state data for validity
     * @return true if state data is valid
     */
    bool CheckMonsterStateData();

    /**
     * @brief Updates the look-at position
     */
    void UpdateLookAtPos();

    /**
     * @brief Updates the look-at position with specific coordinates
     * @param position Position array [x, y, z]
     */
    void UpdateLookAtPos(const float* position);

    /**
     * @brief Checks if emotion presentation should be triggered
     * @param checkType Type of check to perform
     * @param pTarget Target character
     * @return true if emotion presentation should be triggered
     */
    bool CheckEventEmotionPresentation(uint8_t checkType, CCharacter* pTarget);

    /**
     * @brief Clears emotion presentation state
     */
    void ClearEmotionPresentation();

    /**
     * @brief Gets the monster grade/level
     * @return Monster grade
     */
    int GetMonsterGrade() const;

    /**
     * @brief Gets the visual field range
     * @return Visual field range in units
     */
    float GetVisualField() const;

    /**
     * @brief Gets the visual angle
     * @return Visual angle in degrees
     */
    float GetVisualAngle() const;

    /**
     * @brief Gets the Y-axis angle
     * @return Y-axis angle in degrees
     */
    float GetYAngle() const;

    /**
     * @brief Gets the Y-axis angle as a byte
     * @return Y-axis angle as a byte (0-255)
     */
    uint8_t GetYAngleByte() const;

    /**
     * @brief Links event respawn data to the monster
     * @param pEventRespawn Pointer to event respawn data
     */
    void LinkEventRespawn(_event_respawn* pEventRespawn);

    /**
     * @brief Links event set data to the monster
     * @param pEventSet Pointer to event set data
     */
    void LinkEventSet(_event_set* pEventSet);

    /**
     * @brief Changes apparition state
     * @param apparition Whether to enable apparition
     * @param time Time for apparition effect
     */
    void ChangeApparition(bool apparition, uint32_t time);

    /**
     * @brief Destroys the monster with optional notification
     * @param notify Whether to notify about destruction
     * @param pGameObject Game object that triggered destruction
     * @return true if destruction was successful
     */
    bool DestroyMonster(bool notify, CGameObject* pGameObject);

    /**
     * @brief Commands destruction of child monsters
     * @param serial Serial number of child to destroy
     */
    void Command_ChildMonDestroy(uint32_t serial);

    /**
     * @brief Gets the Lua signal reactor
     * @return Pointer to Lua signal reactor
     */
    CLuaSignalReActor* GetSignalReActor();

    /**
     * @brief Gets a new monster serial number
     * @return New unique serial number
     */
    static uint32_t GetNewMonSerial();

private:
    // Member variables (maintaining original structure layout where possible)
    CLootingMgr m_LootMgr;                          ///< Looting manager
    CMonsterAggroMgr m_AggroMgr;                    ///< Aggro management system
    CMonsterHierarchy m_MonHierarcy;                ///< Monster hierarchy system
    MonsterSFContDamageToleracne m_SFContDamageTolerance; ///< Special function damage tolerance
    EmotionPresentationChecker m_EmotionPresentationCheck; ///< Emotion presentation checker
    MonsterStateData m_MonsterStateData;            ///< Current monster state data
    MonsterStateData m_BeforeMonsterStateData;      ///< Previous monster state data
    CCharacter* m_pTargetChar;                      ///< Current target character
    CMonsterSkillPool m_MonsterSkillPool;           ///< Monster skill pool
    CMonsterAI m_AI;                                ///< Monster AI system
    CLuaSignalReActor m_LuaSignalReActor;           ///< Lua signal reactor
    _monster_fld* m_pMonRec;                        ///< Monster record data
    _effect_parameter* m_EP;                        ///< Effect parameters
    _event_respawn* m_pEventRespawn;                ///< Event respawn data
    _event_set* m_pEventSet;                        ///< Event set data
    _mon_active* m_pActiveRec;                      ///< Active record
    _dummy_position* m_pDumPosition;                ///< Dummy position
    uint32_t m_dwObjSerial;                         ///< Object serial number
    uint32_t m_dwDestroyNextTime;                   ///< Time for next destroy check
    uint32_t m_dwLastRecoverTime;                   ///< Last HP recovery time
    uint32_t m_LifeCicle;                           ///< Life cycle timer
    uint32_t m_LifeMax;                             ///< Maximum life time
    int m_nHP;                                      ///< Current health points
    uint32_t m_nCommonStateChunk;                   ///< Common state data chunk
    uint8_t m_bRotateMonster;                       ///< Rotation flag
    uint8_t m_bStdItemLoot;                         ///< Standard item loot flag
    uint8_t m_bRobExp;                              ///< Rob experience flag
    uint8_t m_bRewardExp;                           ///< Reward experience flag
    uint8_t m_bDungeon;                             ///< Dungeon monster flag
    uint8_t m_bApparition;                          ///< Apparition flag
    int m_nEventItemNum;                            ///< Number of event items
    bool m_bLive;                                   ///< Is alive flag

    // Static members
    static int s_nAllocNum;                         ///< Allocation counter

    // Private helper methods
    void InitializeDefaults();
    void InitializeComponents();
    void CleanupResources();
    bool ValidateState() const;
    void ProcessMovement();
    void ProcessCombat();
    void ProcessAI();
    void ProcessLifecycle();
    static void _InitSDM();
    static void _DestroySDM();

    // Disable copy constructor and assignment operator
    CMonster(const CMonster&) = delete;
    CMonster& operator=(const CMonster&) = delete;
};

// Utility functions for monster management
namespace CMonsterUtils {
    /**
     * @brief Creates a new monster instance
     * @return Unique pointer to the created CMonster
     */
    std::unique_ptr<CMonster> CreateMonster();

    /**
     * @brief Validates a monster configuration
     * @param pMonster Pointer to the CMonster to validate
     * @return true if the configuration is valid
     */
    bool ValidateMonster(const CMonster* pMonster);

    /**
     * @brief Gets the memory footprint of a monster
     * @param pMonster Pointer to the CMonster
     * @return Size in bytes of the monster's memory usage
     */
    size_t GetMemoryFootprint(const CMonster* pMonster);
}

// Legacy C-style interface for compatibility
extern "C" {
    void CMonster_Constructor(CMonster* pThis);
    void CMonster_Destructor(CMonster* pThis);
    bool CMonster_IsMovable(CMonster* pThis);
    float CMonster_GetMoveSpeed(CMonster* pThis);
    uint8_t CMonster_GetMoveType(CMonster* pThis);
    void CMonster_SetMoveType(CMonster* pThis, uint8_t moveType);
    int CMonster_GetHP(CMonster* pThis);
    int CMonster_GetMaxHP(CMonster* pThis);
    bool CMonster_SetHP(CMonster* pThis, int hp, bool notify);
    uint8_t CMonster_GetEmotionState(CMonster* pThis);
    void CMonster_SetEmotionState(CMonster* pThis, uint8_t emotionState);
}
